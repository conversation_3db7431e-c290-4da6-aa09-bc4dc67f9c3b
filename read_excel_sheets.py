import openpyxl
import sys

def read_excel_sheets(filename):
    try:
        # 打开Excel文件
        workbook = openpyxl.load_workbook(filename, read_only=True)

        # 获取所有工作表名称
        sheet_names = workbook.sheetnames

        print("Excel文件中的工作表名称:")
        print("=" * 50)
        for i, name in enumerate(sheet_names, 1):
            print(f"{i:2d}. '{name}'")

        print(f"\n总共有 {len(sheet_names)} 个工作表")

        # 检查特定的工作表是否存在
        required_sheets = [
            "UeEUtranMeasurementTDD",
            "UeEUtranMeasurement",
            "CellMeasGroup",
            "CellMeasGroupTDD",
            "EUtranCellTDD",
            "EUtranCellMeasurementTDD",
            "EUtranCellFDD",
            "EUtranCellMeasurement",
            "ECellEquipmentFunction",
            "ECellEquipmentFunctionTDD"
        ]

        print("\n检查必需的工作表:")
        print("=" * 50)
        missing_sheets = []
        for sheet in required_sheets:
            if sheet in sheet_names:
                print(f"✓ {sheet}")
            else:
                print(f"✗ {sheet} (缺失)")
                missing_sheets.append(sheet)

        if missing_sheets:
            print(f"\n缺失的工作表: {missing_sheets}")
        else:
            print("\n所有必需的工作表都存在！")

        workbook.close()

    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

if __name__ == "__main__":
    filename = "RANCM-切换事件-SDR-FTL_D-TDD+FDD简版.xlsx"
    read_excel_sheets(filename)