Sub 粘贴数值()
'
' 粘贴数值 宏
'
' 快捷键: Ctrl+Shift+V
    Selection.PasteSpecial Paste:=xlPasteValues, Operation:=xlNone, SkipBlanks _
        :=False, Transpose:=False '
End Sub

Sub 列转行()
'
' 宏1 Macro
'   主题： 记录的宏 2005-1-19
'



Dim iI As Integer

    Sheets("异频频点结果").Activate
    Cells.Select
    Selection.ClearContents

    Sheets("异频频点").Activate

    I = 2
    k = 2
    Do While Sheets("异频频点").Cells(I, 1) <> ""
       For j = 2 To 33
            If Sheets("异频频点").Cells(I, j) <> "" Then
                Sheets("异频频点结果").Cells(k, 1) = Sheets("异频频点").Cells(I, 1)
                Sheets("异频频点结果").Cells(k, 2) = Sheets("异频频点").Cells(I, j)
                k = k + 1
            End If
        Next j
    
        I = I + 1
    Loop

End Sub
Sub 配置号列转行()
'
' 宏1 Macro
'   主题： 记录的宏 2005-1-19
'



Dim iI As Integer

    Sheets("测量配置号结果").Activate
    Cells.Select
    Selection.ClearContents

    Sheets("测量配置号").Activate

    I = 2
    k = 2
    Do While Sheets("测量配置号").Cells(I, 1) <> ""
       For j = 2 To 33
            If Sheets("测量配置号").Cells(I, j) <> "" Then
                Sheets("测量配置号结果").Cells(k, 1) = Sheets("测量配置号").Cells(I, 1)
                Sheets("测量配置号结果").Cells(k, 2) = Sheets("测量配置号").Cells(I, j)
                k = k + 1
            End If
        Next j
    
        I = I + 1
    Loop

End Sub
Sub 转化常规()
'
' 转化常规 宏
'

'
    Sheets("UeEUtranMeasurementTDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("UeEUtranMeasurement").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("CellMeasGroup").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("CellMeasGroupTDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("EUtranCellTDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("EUtranCellMeasurementTDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("EUtranCellFDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("EUtranCellMeasurement").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("ECellEquipmentFunction").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
    Sheets("ECellEquipmentFunctionTDD").Select
    Range("A1").CurrentRegion.Select
    Selection.NumberFormatLocal = "G/通用格式"
End Sub
Sub 异频载频处理()
'
' 宏1 宏
'

'
    Sheets("EUtranCellMeasurementTDD").Select
    Columns("H:H").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Selection.ClearContents
    'Selection.Delete Shift:=xlToLeft
    Range("H6").Select
    Range("H6").Formula = "=MID(F6,16,6)&""-""&MID(I6,13,3)"
    ' 向下填充公式到最后一行
    Selection.AutoFill Destination:=Range("H6:H" & [B1].End(4).Row)
    Columns("H:H").Select
    Columns("H:H").Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Range("H2").Select
    ActiveCell.FormulaR1C1 = "CI"
    '选取CI和异频载频测量配置
    Range("H:H,CJ:CJ").Select
    Range("CJ1").Activate
    Selection.Copy
    Sheets.Add After:=ActiveSheet
    ActiveSheet.Paste
    Sheets("Sheet1").Select
    Sheets("Sheet1").Name = "异频频点"
    Application.CutCopyMode = False
    Rows("1:1").Select
    Selection.Delete Shift:=xlUp
    Rows("2:4").Select
    Selection.Delete Shift:=xlUp
End Sub
Sub 分列插入结果()
'
' 分列插入结果 宏
'

'
    Columns("B:B").Select
    Selection.TextToColumns Destination:=Range("B1"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
        Semicolon:=False, Comma:=False, Space:=False, Other:=True, OtherChar _
        :=";", FieldInfo:=Array(Array(1, 1), Array(2, 1), Array(3, 1), Array(4, 1), Array(5, _
        1), Array(6, 1), Array(7, 1), Array(8, 1), Array(9, 1), Array(10, 1), Array(11, 1), Array(12 _
        , 1), Array(13, 1), Array(14, 1), Array(15, 1), Array(16, 1)), TrailingMinusNumbers:=True
    Sheets("异频频点").Select
    Sheets.Add After:=ActiveSheet
    Sheets("Sheet2").Select
    Sheets("Sheet2").Name = "异频频点结果"

    Range("A2").Select
    Call 列转行
    Sheets("异频频点结果").Select
    Range("a1") = "CI"
    Range("B1").Select
    ActiveCell.FormulaR1C1 = "针对频点"
    Range("C1").Select
    ActiveCell.FormulaR1C1 = "序号"
    Range("C2").Select
    ActiveCell.FormulaR1C1 = "=IF(RC[-2]=R[-1]C[-2],R[-1]C+1,1)"
    Range("C2").Select
    Selection.AutoFill Destination:=Range("C2:C" & [A1].End(4).Row)
    Range("C2:C" & [A1].End(4).Row).Select

    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub V信息()
'
' 宏4 宏
'

'
    Columns("B:B").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Sheets("EUtranCellTDD").Select
    Columns("I:I").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("I6").Select
    Range("I6").Formula = "=MID(F6,16,6)&""-""&L6"
    Range("I6").Select
    Selection.AutoFill Destination:=Range("I6:I" & [D1].End(4).Row)
    Range("I6:I" & [D1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("异频频点结果").Select
    Range("B1").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "小区名"
    Range("B2").Select
    Range("B2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!H:H)"
    Columns("C:C").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("C1").Select
    ActiveCell.FormulaR1C1 = "自身频点"
    Range("C2").Select
    Range("C2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!AK:AK)"
    Columns("D:D").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("D1").Select
    ActiveCell.FormulaR1C1 = "基带资源"
    Range("D2").Select
    Range("D2").Formula = "=IF(ISERROR(FIND("";"",XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O)))=FALSE,MID(XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O),1,FIND("";"",XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O))-1),XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O))"
    Columns("E:E").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("E1").Select
    ActiveCell.FormulaR1C1 = "基带资源最大传输功率"
    Columns("F:F").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("F1").Select
    ActiveCell.FormulaR1C1 = "基带资源实际发射功率"
    Columns("G:G").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("G1").Select
    ActiveCell.FormulaR1C1 = "基带资源参考信号功率"
    Application.CutCopyMode = False
    Range("E2").Select
    Range("E2").Formula = "=XLOOKUP(D2,ECellEquipmentFunctionTDD!F:F,ECellEquipmentFunctionTDD!AA:AA)"
    Range("F2").Select
    Range("F2").Formula = "=XLOOKUP(D2,ECellEquipmentFunctionTDD!F:F,ECellEquipmentFunctionTDD!AB:AB)"
    Range("G2").Select
    Range("G2").Formula = "=XLOOKUP(D2,ECellEquipmentFunctionTDD!F:F,ECellEquipmentFunctionTDD!AE:AE)"
    Range("B2:G2").Select
    Selection.AutoFill Destination:=Range("B2:G" & [A1].End(4).Row)
    Range("B2:G" & [A1].End(4).Row).Select

    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub 索引组()
' 索引组 宏

    Sheets("EUtranCellMeasurementTDD").Select
    Columns("J:J").Select
    Selection.Replace What:="*TDD=", Replacement:="", LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
        ReplaceFormat:=False
    Columns("I:I").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("I6").Select
    Application.CutCopyMode = False
    Range("I6").Formula = "=left(H6,7)&K6"
    '自动填充
    Range("I6").Select
    Selection.AutoFill Destination:=Range("I6:I" & [A1].End(4).Row)
    '特定行选中
    Range("H6:I" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("异频频点结果").Select
    Columns("E:E").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("E1").Select
    ActiveCell.FormulaR1C1 = "索引组"
    Range("E2").Select
    Range("E2").Formula = "=XLOOKUP(A2,EUtranCellMeasurementTDD!H:H,EUtranCellMeasurementTDD!I:I)"
    Range("E2").Select
    Selection.AutoFill Destination:=Range("E2:E" & [A1].End(4).Row)
    Range("E2:E" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub A1A2()
' A1A2 宏

    Columns("F:F").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("F1").Select
    ActiveCell.FormulaR1C1 = "A1"
    Columns("G:G").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("G1").Select
    ActiveCell.FormulaR1C1 = "A2"
    Sheets("CellMeasGroupTDD").Select
    Columns("J:J").Select
    ActiveWindow.SmallScroll ToRight:=2
    Columns("J:K").Select
    '替换特定符号后面的内容
    Selection.Replace What:=";*", Replacement:="", LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
        ReplaceFormat:=False
    Columns("I:I").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("I6").Select
    Application.CutCopyMode = False
    '插入MID公式
    Range("I6").Formula = "=MID(F6,16,6)&""-""&J6"
    Range("I6").Select
    Selection.AutoFill Destination:=Range("I6:I" & [A1].End(4).Row)
    Range("I6:I" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("异频频点结果").Select
    Range("F2").Select
    Application.CutCopyMode = False
    Range("F2").Formula = "=XLOOKUP(E2,CellMeasGroupTDD!I:I,CellMeasGroupTDD!K:K)"
    Range("G2").Select
    Range("G2").Formula = "=XLOOKUP(E2,CellMeasGroupTDD!I:I,CellMeasGroupTDD!L:L)"
    Range("F2:G2").Select
    Selection.AutoFill Destination:=Range("F2:G" & [A1].End(4).Row)
    Range("F2:G" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub 测量配置号()
'
' 测量配置号 宏
'

'
    Range("M1").Select
    ActiveCell.FormulaR1C1 = "索引组+序号"
    Range("M2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=RC[-8]&""-""&RC[-1]"
    Range("M2").Select
    Selection.AutoFill Destination:=Range("M2:M" & [A1].End(4).Row)
    Range("M2:M" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("CellMeasGroupTDD").Select
    '特定列复制
    Range("I:I,P:P").Select
    Range("P1").Activate
    Application.CutCopyMode = False
    Selection.Copy
    Sheets.Add After:=ActiveSheet
    ActiveSheet.Paste
    Sheets("Sheet3").Select
    Sheets("Sheet3").Name = "测量配置号"
    Range("A2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "索引组"
    Rows("1:1").Select
    Selection.Delete Shift:=xlUp
    Rows("2:4").Select
    Selection.Delete Shift:=xlUp
    Sheets.Add After:=ActiveSheet
    Sheets("Sheet4").Select
    Sheets("Sheet4").Name = "测量配置号结果"
    Sheets("测量配置号").Select
    Columns("B:B").Select
    Selection.TextToColumns Destination:=Range("B1"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
        Semicolon:=False, Comma:=False, Space:=False, Other:=True, OtherChar _
        :=";", FieldInfo:=Array(Array(1, 1), Array(2, 1), Array(3, 1), Array(4, 1), Array(5, _
        1), Array(6, 1), Array(7, 1), Array(8, 1), Array(9, 1), Array(10, 1), Array(11, 1), Array(12 _
        , 1), Array(13, 1), Array(14, 1), Array(15, 1), Array(16, 1), Array(17, 1), Array(18, 1), _
        Array(19, 1), Array(20, 1), Array(21, 1), Array(22, 1), Array(23, 1), Array(24, 1), Array( _
        25, 1), Array(26, 1), Array(27, 1), Array(28, 1), Array(29, 1), Array(30, 1), Array(31, 1), _
        Array(32, 1)), TrailingMinusNumbers:=True
    Columns("R:R").Select
    Range(Selection, Selection.End(xlToRight)).Select
    Selection.Delete Shift:=xlToLeft
    Call 配置号列转行
    Sheets("测量配置号结果").Select
    Range("a1") = "索引组"
    Range("B1").Select
    ActiveCell.FormulaR1C1 = "测量配置号"
    Range("C1").Select
    ActiveCell.FormulaR1C1 = "序号"
    Range("C2").Select
    ActiveCell.FormulaR1C1 = "=IF(RC[-2]=R[-1]C[-2],R[-1]C+1,1)"
    Range("C2").Select
    Selection.AutoFill Destination:=Range("C2:C" & [A1].End(4).Row)
    Range("C2:C" & [A1].End(4).Row).Select

    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub v配置号()
'
' v配置号 宏
'

'
    Columns("B:B").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("B2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=RC[-1]&""-""&RC[2]"
    Range("B2").Select
    Selection.AutoFill Destination:=Range("B2:B" & [A1].End(4).Row)
    Range("B2:B" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("异频频点结果").Select
    Range("N1").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "测量配置号"
    Range("N2").Select
    ActiveCell.FormulaR1C1 = "=VLOOKUP(RC[-1],测量配置号结果!C[-12]:C[-11],2,0)"
    Range("N2").Select
    Selection.AutoFill Destination:=Range("N2:N" & [A1].End(4).Row)
    Range("N2:N" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub
Sub v门限()
' v门限 宏
'
    Range("O1").Select
    ActiveCell.FormulaR1C1 = "基站号+测量配置号"
    Range("O2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=LEFT(RC[-14],6)&""-""&RC[-1]"
    Range("O2").Select
    Selection.AutoFill Destination:=Range("O2:O" & [A1].End(4).Row)
    Range("O2:O" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("UeEUtranMeasurementTDD").Select
    Columns("M:M").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("M6").Select
    Application.CutCopyMode = False
    Range("M6").Formula = "=MID(F6,16,6)&""-""&H6"
    Range("M6").Select
    Selection.AutoFill Destination:=Range("M6:M" & [A1].End(4).Row)
    Range("M6:M" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("异频频点结果").Select
    Columns("G:G").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("G1").Select
    ActiveCell.FormulaR1C1 = "A1门限"
    Columns("I:I").Select
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("I1").Select
    ActiveCell.FormulaR1C1 = "A2门限"
    Range("R2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=LEFT(RC[-1],6)&""-""&RC[-12]"
    Range("S2").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "=LEFT(RC[-2],6)&""-""&RC[-11]"
    Range("R2:S2").Select
    Selection.AutoFill Destination:=Range("R2:S" & [A1].End(4).Row)
    Range("R2:S" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    '插入xlookup公式
    Range("G2").Select
    Range("G2").Formula = "=XLOOKUP(R2,UeEUtranMeasurementTDD!M:M,UeEUtranMeasurementTDD!O:O)"
    Range("G2").Select
    Selection.AutoFill Destination:=Range("G2:G" & [A1].End(4).Row)
    Range("G2:G" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Range("I2").Select
    Range("I2").Formula = "=XLOOKUP(S2,UeEUtranMeasurementTDD!M:M,UeEUtranMeasurementTDD!O:O)"
    Range("I2").Select
    Selection.AutoFill Destination:=Range("I2:I" & [A1].End(4).Row)
    Range("I2:I" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Columns("J:J").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("J1").Select
    ActiveCell.FormulaR1C1 = "事件"
    Range("J2").Select
    Range("J2").Formula = "=XLOOKUP(R2,UeEUtranMeasurementTDD!M:M,UeEUtranMeasurementTDD!N:N)"
    Range("J2").Select
    Selection.AutoFill Destination:=Range("J2:J" & [A1].End(4).Row)
    Range("J2:J" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Columns("B:B").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("B1").Select
    ActiveCell.FormulaR1C1 = "基站号"
    Range("B2").Select
    Range("B2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!D:D)"
    Range("B2").Select
    Selection.AutoFill Destination:=Range("B2:B" & [A1].End(4).Row)
    Range("B2:B" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
    Sheets("UeEUtranMeasurementTDD").Select
    Rows("5:5").Select
    Range("E5").Activate
    Application.CutCopyMode = False
    Selection.AutoFilter
    ActiveSheet.Range("A5:AF" & [A1].End(4).Row).AutoFilter Field:=8, Criteria1:="250"
    Range("A1:AF" & [A1].End(4).Row).Select
'    Range("G470").Activate
    Selection.Copy
    Sheets.Add After:=ActiveSheet
    ActiveSheet.Paste
    Sheets("Sheet5").Select
    Sheets("Sheet5").Name = "250"
    Sheets("异频频点结果").Select
    Range("T1").Select
    Application.CutCopyMode = False
    ActiveCell.FormulaR1C1 = "测量配置号250门限"
    Range("T2").Select
    ActiveCell.FormulaR1C1 = "=VLOOKUP(RC[-18],'250'!C[-16]:C[-5],12,0)"
    Range("T2").Select
    Selection.AutoFill Destination:=Range("T2:T" & [A1].End(4).Row)
    Range("T2:T" & [A1].End(4).Row).Select
    Selection.Copy
    Application.Run "切换策略脚本.xlsm!粘贴数值"
End Sub

Sub 整理结果()
'
' 整理结果 宏
'

'
    Columns("U:U").Select
    Selection.Delete Shift:=xlToLeft
    Columns("Q:Q").Select
    Selection.Delete Shift:=xlToLeft
    Columns("O:R").Select
    Selection.Copy
    Columns("E:E").Select
    Selection.Insert Shift:=xlToRight
    Columns("J:J").Select
    Application.CutCopyMode = False
    Selection.Copy
    Columns("D:D").Select
    Selection.Insert Shift:=xlToRight
    Columns("K:K").Select
    Application.CutCopyMode = False
    Selection.Delete Shift:=xlToLeft
    Columns("S:V").Select
    Selection.Delete Shift:=xlToLeft
End Sub
Sub v门限1和门限2并整理TDD()
'
' 宏2 宏
    Range("T1").Select
    ActiveCell.FormulaR1C1 = "门限1"
    Range("U1").Select
    ActiveCell.FormulaR1C1 = "门限2"
    Range("T2").Select
    Range("T2").Formula = "=XLOOKUP(I2,UeEUtranMeasurementTDD!M:M,UeEUtranMeasurementTDD!O:O)"
    Range("U2").Select
    Range("U2").Formula = "=XLOOKUP(I2,UeEUtranMeasurementTDD!M:M,UeEUtranMeasurementTDD!Q:Q)"
    Range("T2:U2").Select
    Selection.AutoFill Destination:=Range("T2:U" & [A1].End(4).Row)
    Range("T2:U" & [A1].End(4).Row).Select
    Selection.Copy
    Call 粘贴数值
    Range("V2").Select
    Range("V2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!S:S)"
    Range("W2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!Y:Y)"
    Range("X2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!AJ:AJ)"
    Range("Y2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!AK:AK)"
    Range("Z2").Formula = "=XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!AR:AR)"
    Range("V1").Select
    ActiveCell.FormulaR1C1 = "PCI"
    Range("W1").Select
    ActiveCell.FormulaR1C1 = "TAC"
    Range("X1").Select
    ActiveCell.FormulaR1C1 = "频段指示"
    Range("Y1").Select
    ActiveCell.FormulaR1C1 = "中心频点"
    Range("Z1").Select
    ActiveCell.FormulaR1C1 = "带宽"
    Range("V2:Z2").Select
    Selection.AutoFill Destination:=Range("V2:Z" & [A1].End(4).Row)
    Range("V2:Z" & [A1].End(4).Row).Select
    Selection.Copy
    Call 粘贴数值
    Columns("G:G").Select
    Application.CutCopyMode = False
    Selection.Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    Range("G1").Select
    ActiveCell.FormulaR1C1 = "基于覆盖的异频载频测量结果"
    Range("G2").Select
    Range("G2").Formula = "=XLOOKUP(D2,CellMeasGroupTDD!I:I,CellMeasGroupTDD!P:P)"
    Range("G2").Select
    Selection.AutoFill Destination:=Range("G2:G" & [A1].End(4).Row)
    Range("G2:G" & [A1].End(4).Row).Select
    Selection.Copy
    Call 粘贴数值
    Columns("K:K").Select
    
    ' 使用不常见的组合作为标记
    Dim specialMark As String
    specialMark = "###MARK###"  ' 确保这个字符串不会出现在原始数据中
    
    ' 步骤1：将最后一个等号替换为特殊标记
    Selection.Replace What:="=*=", Replacement:=specialMark, LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
        ReplaceFormat:=False, MatchByte:=False
        
    ' 步骤2：删除特殊标记前面的所有内容
    Selection.Replace What:="*" & specialMark, Replacement:="", LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
        ReplaceFormat:=False, MatchByte:=False

End Sub
Sub 调用全部()
    Call 转化常规
    Call 异频载频处理
    Call 分列插入结果
    Call V信息
    Call 索引组
    Call A1A2
    Call 测量配置号
    Call v配置号
    Call v门限
    Call 整理结果
    Call v门限1和门限2并整理TDD
End Sub

