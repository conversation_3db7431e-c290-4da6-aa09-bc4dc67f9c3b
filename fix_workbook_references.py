import re

def fix_workbook_references():
    filename = "TDD类事件整理_优化版.bas"

    try:
        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换所有的ThisWorkbook为ActiveWorkbook
        # 但保留注释中的ThisWorkbook
        content = re.sub(r'(?<![\'"#])ThisWorkbook', 'ActiveWorkbook', content)

        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        print("成功替换所有ThisWorkbook为ActiveWorkbook")

    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    fix_workbook_references()