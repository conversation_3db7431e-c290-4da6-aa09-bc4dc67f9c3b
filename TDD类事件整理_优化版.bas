' ========================================
' TDD类事件整理 - 优化版本
' 优化目标：提高性能、增强健壮性、减少重复代码
' ========================================

Option Explicit

' 全局变量用于性能优化
Private Type AppSettings
    ScreenUpdating As Boolean
    Calculation As Long
    EnableEvents As Boolean
End Type

' ========================================
' 核心优化函数
' ========================================

' 优化后的粘贴数值函数
Sub 粘贴数值()
    On Error GoTo ErrorHandler

    If Selection Is Nothing Then Exit Sub

    ' 使用更高效的方式粘贴数值
    With Selection
        .Value = .Value  ' 直接将值赋给自身，自动转换为数值
    End With

    Exit Sub
ErrorHandler:
    MsgBox "粘贴数值操作失败: " & Err.Description, vbCritical
End Sub

' 通用列转行函数 - 消除重复代码
Sub 通用列转行(ByVal sourceSheetName As String, ByVal targetSheetName As String, _
              Optional ByVal startCol As Long = 2, Optional ByVal endCol As Long = 33)

    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim wsSource As Worksheet, wsTarget As Worksheet
    Dim sourceData As Variant, targetData As Variant
    Dim lastRow As Long, outputRow As Long
    Dim i As Long, j As Long, targetIndex As Long

    ' 验证并获取工作表
    If Not 验证工作表存在(sourceSheetName) Then
        MsgBox "源工作表 '" & sourceSheetName & "' 不存在！", vbCritical
        GoTo Cleanup
    End If

    Set wsSource = ThisWorkbook.Worksheets(sourceSheetName)

    ' 创建或清空目标工作表
    Call 创建或清空工作表(targetSheetName, wsTarget)

    ' 获取源数据范围
    lastRow = wsSource.Cells(wsSource.Rows.Count, 1).End(xlUp).Row
    If lastRow < 2 Then GoTo Cleanup ' 没有数据

    ' 检查数据范围有效性
    If endCol > wsSource.Columns.Count Then endCol = wsSource.Columns.Count

    ' 一次性读取所有源数据到数组
    sourceData = wsSource.Range(wsSource.Cells(2, 1), wsSource.Cells(lastRow, endCol)).Value

    ' 检查数组是否有效
    If IsEmpty(sourceData) Then GoTo Cleanup

    ' 预估目标数据大小并创建数组
    Dim estimatedSize As Long
    estimatedSize = (lastRow - 1) * (endCol - startCol + 1)
    ReDim targetData(1 To estimatedSize, 1 To 2)

    targetIndex = 0

    ' 处理数据转换 - 添加更严格的边界检查
    For i = 1 To UBound(sourceData, 1)
        For j = startCol To endCol
            ' 确保j在有效范围内
            If j <= UBound(sourceData, 2) And j >= 1 Then
                If sourceData(i, j) <> "" And Not IsEmpty(sourceData(i, j)) Then
                    targetIndex = targetIndex + 1
                    If targetIndex <= estimatedSize Then
                        targetData(targetIndex, 1) = sourceData(i, 1)  ' 第一列数据
                        targetData(targetIndex, 2) = sourceData(i, j)  ' 对应列数据
                    End If
                End If
            End If
        Next j
    Next i

    ' 一次性写入目标数据
    If targetIndex > 0 Then
        ' 调整数组大小以匹配实际数据
        ReDim Preserve targetData(1 To targetIndex, 1 To 2)
        wsTarget.Range(wsTarget.Cells(2, 1), wsTarget.Cells(targetIndex + 1, 2)).Value = targetData
    End If

Cleanup:
    Call 恢复应用设置(appSettings)
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    MsgBox "列转行操作失败: " & Err.Description & vbCrLf & "错误位置: " & Err.Source, vbCritical
End Sub

' 优化后的列转行函数
Sub 列转行()
    Call 通用列转行("异频频点", "异频频点结果")
End Sub

' 优化后的配置号列转行函数
Sub 配置号列转行()
    Call 通用列转行("测量配置号", "测量配置号结果")
End Sub

' 优化后的转化常规函数
Sub 转化常规()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim sheetNames As Variant
    Dim i As Long

    ' 定义需要处理的工作表名称
    sheetNames = Array("UeEUtranMeasurementTDD", "UeEUtranMeasurement", "CellMeasGroup", _
                      "CellMeasGroupTDD", "EUtranCellTDD", "EUtranCellMeasurementTDD", _
                      "EUtranCellFDD", "EUtranCellMeasurement", "ECellEquipmentFunction", _
                      "ECellEquipmentFunctionTDD")

    ' 批量处理所有工作表
    For i = 0 To UBound(sheetNames)
        If 验证工作表存在(CStr(sheetNames(i))) Then
            Call 设置工作表数字格式(CStr(sheetNames(i)), "G/通用格式")
        End If
    Next i

Cleanup:
    Call 恢复应用设置(appSettings)
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    MsgBox "转化常规操作失败: " & Err.Description, vbCritical
End Sub

' ========================================
' 辅助函数
' ========================================

' 保存并优化应用程序设置
Private Sub 保存并优化应用设置(ByRef settings As AppSettings)
    With Application
        settings.ScreenUpdating = .ScreenUpdating
        settings.Calculation = .Calculation
        settings.EnableEvents = .EnableEvents

        .ScreenUpdating = False
        .Calculation = xlCalculationManual
        .EnableEvents = False
    End With
End Sub

' 恢复应用程序设置
Private Sub 恢复应用设置(ByRef settings As AppSettings)
    With Application
        .ScreenUpdating = settings.ScreenUpdating
        .Calculation = settings.Calculation
        .EnableEvents = settings.EnableEvents
    End With
End Sub

' 验证工作表是否存在
Private Function 验证工作表存在(ByVal sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(sheetName)
    验证工作表存在 = Not (ws Is Nothing)
    On Error GoTo 0
    Set ws = Nothing
End Function

' 创建或清空工作表
Private Sub 创建或清空工作表(ByVal sheetName As String, ByRef ws As Worksheet)
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(sheetName)
    On Error GoTo 0

    If ws Is Nothing Then
        Set ws = ThisWorkbook.Worksheets.Add
        ws.Name = sheetName
    Else
        ws.Cells.Clear
    End If
End Sub

' 设置工作表数字格式
Private Sub 设置工作表数字格式(ByVal sheetName As String, ByVal numberFormat As String)
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(sheetName)

    Dim dataRange As Range
    Set dataRange = ws.Range("A1").CurrentRegion

    If Not dataRange Is Nothing Then
        dataRange.NumberFormatLocal = numberFormat
    End If
End Sub

' 高效的公式填充函数
Private Sub 高效填充公式(ByVal ws As Worksheet, ByVal startCell As String, _
                    ByVal formula As String, ByVal lastRow As Long)

    Dim rng As Range
    Set rng = ws.Range(startCell)
    rng.formula = formula

    If lastRow > rng.Row Then
        rng.AutoFill Destination:=ws.Range(rng, ws.Cells(lastRow, rng.Column))
    End If
End Sub

' 优化的异频载频处理函数
Sub 异频载频处理()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("EUtranCellMeasurementTDD")

    ' 插入新列
    ws.Columns("H:H").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    ' 获取数据范围
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).Row

    ' 批量设置公式
    Call 高效填充公式(ws, "H6", "=MID(F6,16,6)&""-""&MID(I6,13,3)", lastRow)

    ' 转换为数值
    With ws.Range("H6:H" & lastRow)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 设置标题
    ws.Range("H2").Value = "CI"

    ' 复制数据到新工作表
    Call 复制数据到新工作表(ws, "H:H,CJ:CJ", "异频频点")

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "异频载频处理失败: " & Err.Description, vbCritical
End Sub

' 复制数据到新工作表的辅助函数
Private Sub 复制数据到新工作表(ByVal sourceWs As Worksheet, ByVal rangeAddress As String, ByVal newSheetName As String)
    sourceWs.Range(rangeAddress).Copy

    Dim newWs As Worksheet
    Set newWs = ThisWorkbook.Worksheets.Add(After:=sourceWs)
    newWs.Name = newSheetName
    newWs.Paste

    ' 删除不需要的行
    newWs.Rows("1:1").Delete Shift:=xlUp
    newWs.Rows("2:4").Delete Shift:=xlUp
End Sub

' 优化的分列插入结果函数
Sub 分列插入结果()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim ws As Worksheet
    Set ws = ActiveSheet

    ' 分列操作
    ws.Columns("B:B").TextToColumns Destination:=ws.Range("B1"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
        Semicolon:=False, Comma:=False, Space:=False, Other:=True, OtherChar:=";", _
        TrailingMinusNumbers:=True

    ' 创建结果工作表
    Dim resultWs As Worksheet
    Call 创建或清空工作表("异频频点结果", resultWs)

    ' 调用列转行
    Call 列转行

    ' 设置标题和公式
    With resultWs
        .Range("A1").Value = "CI"
        .Range("B1").Value = "针对频点"
        .Range("C1").Value = "序号"

        Dim lastRow As Long
        lastRow = .Cells(.Rows.Count, "A").End(xlUp).Row

        If lastRow > 1 Then
            Call 高效填充公式(resultWs, "C2", "=IF(RC[-2]=R[-1]C[-2],R[-1]C+1,1)", lastRow)

            ' 转换为数值
            With .Range("C2:C" & lastRow)
                .Copy
                .PasteSpecial Paste:=xlPasteValues
            End With
        End If
    End With

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "分列插入结果失败: " & Err.Description, vbCritical
End Sub

' 优化的调用全部函数
Sub 调用全部()
    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 保存应用程序设置
    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    ' 显示进度
    Application.StatusBar = "正在执行数据处理..."

    ' 按顺序调用所有处理函数
    Application.StatusBar = "正在转化常规格式..."
    Call 转化常规

    Application.StatusBar = "正在处理异频载频..."
    Call 异频载频处理

    Application.StatusBar = "正在分列插入结果..."
    Call 分列插入结果

    Application.StatusBar = "正在处理V信息..."
    Call V信息

    Application.StatusBar = "正在处理索引组..."
    Call 索引组

    Application.StatusBar = "正在处理A1A2..."
    Call A1A2

    Application.StatusBar = "正在处理测量配置号..."
    Call 测量配置号

    Application.StatusBar = "正在处理v配置号..."
    Call v配置号

    Application.StatusBar = "正在处理v门限..."
    Call v门限

    Application.StatusBar = "正在整理结果..."
    Call 整理结果

    Application.StatusBar = "正在处理门限1和门限2..."
    Call v门限1和门限2并整理TDD

    ' 完成
    Dim endTime As Double
    endTime = Timer

    ' 恢复应用程序设置
    Call 恢复应用设置(appSettings)

    Application.StatusBar = "处理完成！用时: " & Format(endTime - startTime, "0.00") & " 秒"

    MsgBox "数据处理完成！" & vbCrLf & "用时: " & Format(endTime - startTime, "0.00") & " 秒", vbInformation

    Exit Sub

ErrorHandler:
    ' 确保恢复应用程序设置
    Call 恢复应用设置(appSettings)
    Application.StatusBar = "处理过程中发生错误"
    MsgBox "处理过程中发生错误: " & Err.Description & vbCrLf & "错误编号: " & Err.Number & vbCrLf & "请检查工作表是否存在", vbCritical
End Sub

' 批量XLOOKUP优化函数
Private Sub 批量设置XLOOKUP公式(ByVal ws As Worksheet, ByVal targetRange As String, _
                           ByVal lookupRange As String, ByVal tableArray As String, _
                           ByVal returnArray As String, ByVal lastRow As Long)

    Dim formula As String
    formula = "=XLOOKUP(" & lookupRange & "," & tableArray & "," & returnArray & ")"

    Call 高效填充公式(ws, targetRange, formula, lastRow)

    ' 转换为数值
    With ws.Range(targetRange & ":" & Split(targetRange, ":")(0) & lastRow)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With
End Sub

' 以下是原始函数的优化版本，保持相同的输出结果但提高性能

' 优化的V信息函数
Sub V信息()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    ' 处理EUtranCellTDD工作表
    Dim wsEUtran As Worksheet
    Set wsEUtran = ThisWorkbook.Worksheets("EUtranCellTDD")

    ' 插入列并设置公式
    wsEUtran.Columns("I:I").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    Dim lastRowEUtran As Long
    lastRowEUtran = wsEUtran.Cells(wsEUtran.Rows.Count, "D").End(xlUp).Row

    Call 高效填充公式(wsEUtran, "I6", "=MID(F6,16,6)&""-""&L6", lastRowEUtran)

    ' 转换为数值
    With wsEUtran.Range("I6:I" & lastRowEUtran)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 处理异频频点结果工作表
    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    ' 批量插入列并设置标题
    wsResult.Columns("B:B").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("B1").Value = "小区名"

    wsResult.Columns("C:C").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("C1").Value = "自身频点"

    wsResult.Columns("D:D").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("D1").Value = "基带资源"

    wsResult.Columns("E:E").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("E1").Value = "基带资源最大传输功率"

    wsResult.Columns("F:F").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("F1").Value = "基带资源实际发射功率"

    wsResult.Columns("G:G").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("G1").Value = "基带资源参考信号功率"

    ' 批量设置XLOOKUP公式
    Call 批量设置XLOOKUP公式(wsResult, "B2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!H:H", lastRowResult)
    Call 批量设置XLOOKUP公式(wsResult, "C2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!AK:AK", lastRowResult)

    ' 设置基带资源公式（复杂公式）
    Call 高效填充公式(wsResult, "D2", "=IF(ISERROR(FIND("";"",XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O)))=FALSE,MID(XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O),1,FIND("";"",XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O))-1),XLOOKUP(A2,EUtranCellTDD!I:I,EUtranCellTDD!O:O))", lastRowResult)

    Call 批量设置XLOOKUP公式(wsResult, "E2", "D2", "ECellEquipmentFunctionTDD!F:F", "ECellEquipmentFunctionTDD!AA:AA", lastRowResult)
    Call 批量设置XLOOKUP公式(wsResult, "F2", "D2", "ECellEquipmentFunctionTDD!F:F", "ECellEquipmentFunctionTDD!AB:AB", lastRowResult)
    Call 批量设置XLOOKUP公式(wsResult, "G2", "D2", "ECellEquipmentFunctionTDD!F:F", "ECellEquipmentFunctionTDD!AE:AE", lastRowResult)

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "V信息处理失败: " & Err.Description, vbCritical
End Sub

' 优化的索引组函数
Sub 索引组()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("EUtranCellMeasurementTDD")

    ' 批量替换操作
    ws.Columns("J:J").Replace What:="*TDD=", Replacement:="", LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, ReplaceFormat:=False

    ' 插入列并设置公式
    ws.Columns("I:I").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    Call 高效填充公式(ws, "I6", "=left(H6,7)&K6", lastRow)

    ' 转换为数值
    With ws.Range("H6:I" & lastRow)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 处理异频频点结果工作表
    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    wsResult.Columns("E:E").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("E1").Value = "索引组"

    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    Call 批量设置XLOOKUP公式(wsResult, "E2", "A2", "EUtranCellMeasurementTDD!H:H", "EUtranCellMeasurementTDD!I:I", lastRowResult)

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "索引组处理失败: " & Err.Description, vbCritical
End Sub

' 优化的A1A2函数
Sub A1A2()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    ' 处理异频频点结果工作表
    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    ' 插入A1和A2列
    wsResult.Columns("F:F").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("F1").Value = "A1"

    wsResult.Columns("G:G").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("G1").Value = "A2"

    ' 处理CellMeasGroupTDD工作表
    Dim wsCellMeas As Worksheet
    Set wsCellMeas = ThisWorkbook.Worksheets("CellMeasGroupTDD")

    ' 批量替换操作
    wsCellMeas.Columns("J:K").Replace What:=";*", Replacement:="", LookAt:=xlPart, _
        SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, ReplaceFormat:=False

    ' 插入列并设置公式
    wsCellMeas.Columns("I:I").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    Dim lastRowCellMeas As Long
    lastRowCellMeas = wsCellMeas.Cells(wsCellMeas.Rows.Count, "A").End(xlUp).Row

    Call 高效填充公式(wsCellMeas, "I6", "=MID(F6,16,6)&""-""&J6", lastRowCellMeas)

    ' 转换为数值
    With wsCellMeas.Range("I6:I" & lastRowCellMeas)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 设置异频频点结果的XLOOKUP公式
    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    Call 批量设置XLOOKUP公式(wsResult, "F2", "E2", "CellMeasGroupTDD!I:I", "CellMeasGroupTDD!K:K", lastRowResult)
    Call 批量设置XLOOKUP公式(wsResult, "G2", "E2", "CellMeasGroupTDD!I:I", "CellMeasGroupTDD!L:L", lastRowResult)

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "A1A2处理失败: " & Err.Description, vbCritical
End Sub

' 优化的测量配置号函数
Sub 测量配置号()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    ' 处理异频频点结果工作表
    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    ' 设置索引组+序号列
    wsResult.Range("M1").Value = "索引组+序号"
    Call 高效填充公式(wsResult, "M2", "=RC[-8]&""-""&RC[-1]", lastRowResult)

    ' 转换为数值
    With wsResult.Range("M2:M" & lastRowResult)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 处理CellMeasGroupTDD工作表数据
    Dim wsCellMeas As Worksheet
    Set wsCellMeas = ThisWorkbook.Worksheets("CellMeasGroupTDD")

    ' 复制数据到新工作表
    wsCellMeas.Range("I:I,P:P").Copy

    Dim wsConfig As Worksheet
    Set wsConfig = ThisWorkbook.Worksheets.Add(After:=wsCellMeas)
    wsConfig.Name = "测量配置号"
    wsConfig.Paste

    ' 设置标题
    wsConfig.Range("A2").Value = "索引组"
    wsConfig.Rows("1:1").Delete Shift:=xlUp
    wsConfig.Rows("2:4").Delete Shift:=xlUp

    ' 创建结果工作表
    Dim wsConfigResult As Worksheet
    Set wsConfigResult = ThisWorkbook.Worksheets.Add(After:=wsConfig)
    wsConfigResult.Name = "测量配置号结果"

    ' 分列操作
    wsConfig.Columns("B:B").TextToColumns Destination:=wsConfig.Range("B1"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
        Semicolon:=False, Comma:=False, Space:=False, Other:=True, OtherChar:=";", _
        TrailingMinusNumbers:=True

    ' 删除多余列
    Dim lastCol As Long
    lastCol = wsConfig.Cells(1, wsConfig.Columns.Count).End(xlToLeft).Column
    If lastCol > 17 Then
        wsConfig.Range(wsConfig.Cells(1, 18), wsConfig.Cells(1, lastCol)).EntireColumn.Delete
    End If

    ' 调用配置号列转行
    Call 配置号列转行

    ' 设置结果工作表标题和公式
    With wsConfigResult
        .Range("A1").Value = "索引组"
        .Range("B1").Value = "测量配置号"
        .Range("C1").Value = "序号"

        Dim lastRowConfig As Long
        lastRowConfig = .Cells(.Rows.Count, "A").End(xlUp).Row

        If lastRowConfig > 1 Then
            Call 高效填充公式(wsConfigResult, "C2", "=IF(RC[-2]=R[-1]C[-2],R[-1]C+1,1)", lastRowConfig)

            ' 转换为数值
            With .Range("C2:C" & lastRowConfig)
                .Copy
                .PasteSpecial Paste:=xlPasteValues
            End With
        End If
    End With

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "测量配置号处理失败: " & Err.Description, vbCritical
End Sub

' 优化的v配置号函数
Sub v配置号()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim wsConfigResult As Worksheet
    Set wsConfigResult = ThisWorkbook.Worksheets("测量配置号结果")

    ' 插入新列并设置公式
    wsConfigResult.Columns("B:B").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    Dim lastRowConfig As Long
    lastRowConfig = wsConfigResult.Cells(wsConfigResult.Rows.Count, "A").End(xlUp).Row

    Call 高效填充公式(wsConfigResult, "B2", "=RC[-1]&""-""&RC[2]", lastRowConfig)

    ' 转换为数值
    With wsConfigResult.Range("B2:B" & lastRowConfig)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 处理异频频点结果工作表
    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    wsResult.Range("N1").Value = "测量配置号"

    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    ' 使用VLOOKUP公式
    Call 高效填充公式(wsResult, "N2", "=VLOOKUP(RC[-1],测量配置号结果!C[-12]:C[-11],2,0)", lastRowResult)

    ' 转换为数值
    With wsResult.Range("N2:N" & lastRowResult)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "v配置号处理失败: " & Err.Description, vbCritical
End Sub

' 优化的v门限函数
Sub v门限()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim wsResult As Worksheet
    Set wsResult = ThisWorkbook.Worksheets("异频频点结果")

    Dim lastRowResult As Long
    lastRowResult = wsResult.Cells(wsResult.Rows.Count, "A").End(xlUp).Row

    ' 设置基站号+测量配置号列
    wsResult.Range("O1").Value = "基站号+测量配置号"
    Call 高效填充公式(wsResult, "O2", "=LEFT(RC[-14],6)&""-""&RC[-1]", lastRowResult)

    ' 转换为数值
    With wsResult.Range("O2:O" & lastRowResult)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 处理UeEUtranMeasurementTDD工作表
    Dim wsUeMeas As Worksheet
    Set wsUeMeas = ThisWorkbook.Worksheets("UeEUtranMeasurementTDD")

    wsUeMeas.Columns("M:M").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove

    Dim lastRowUeMeas As Long
    lastRowUeMeas = wsUeMeas.Cells(wsUeMeas.Rows.Count, "A").End(xlUp).Row

    Call 高效填充公式(wsUeMeas, "M6", "=MID(F6,16,6)&""-""&H6", lastRowUeMeas)

    ' 转换为数值
    With wsUeMeas.Range("M6:M" & lastRowUeMeas)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 在异频频点结果中插入门限列
    wsResult.Columns("G:G").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("G1").Value = "A1门限"

    wsResult.Columns("I:I").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("I1").Value = "A2门限"

    ' 设置辅助列公式
    Call 高效填充公式(wsResult, "R2", "=LEFT(RC[-1],6)&""-""&RC[-12]", lastRowResult)
    Call 高效填充公式(wsResult, "S2", "=LEFT(RC[-2],6)&""-""&RC[-11]", lastRowResult)

    ' 转换为数值
    With wsResult.Range("R2:S" & lastRowResult)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 设置XLOOKUP公式
    Call 批量设置XLOOKUP公式(wsResult, "G2", "R2", "UeEUtranMeasurementTDD!M:M", "UeEUtranMeasurementTDD!O:O", lastRowResult)
    Call 批量设置XLOOKUP公式(wsResult, "I2", "S2", "UeEUtranMeasurementTDD!M:M", "UeEUtranMeasurementTDD!O:O", lastRowResult)

    ' 插入事件列
    wsResult.Columns("J:J").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("J1").Value = "事件"

    Call 批量设置XLOOKUP公式(wsResult, "J2", "R2", "UeEUtranMeasurementTDD!M:M", "UeEUtranMeasurementTDD!N:N", lastRowResult)

    ' 插入基站号列
    wsResult.Columns("B:B").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    wsResult.Range("B1").Value = "基站号"

    Call 批量设置XLOOKUP公式(wsResult, "B2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!D:D", lastRowResult)

    ' 处理250门限
    Call 处理250门限(wsUeMeas, wsResult, lastRowResult)

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "v门限处理失败: " & Err.Description, vbCritical
End Sub

' 处理250门限的辅助函数
Private Sub 处理250门限(ByVal wsUeMeas As Worksheet, ByVal wsResult As Worksheet, ByVal lastRowResult As Long)
    ' 设置自动筛选
    Dim lastRowUeMeas As Long
    lastRowUeMeas = wsUeMeas.Cells(wsUeMeas.Rows.Count, "A").End(xlUp).Row

    wsUeMeas.Range("A5:AF" & lastRowUeMeas).AutoFilter Field:=8, Criteria1:="250"

    ' 复制筛选结果到新工作表
    wsUeMeas.Range("A1:AF" & lastRowUeMeas).Copy

    Dim ws250 As Worksheet
    Set ws250 = ThisWorkbook.Worksheets.Add(After:=wsUeMeas)
    ws250.Name = "250"
    ws250.Paste

    ' 设置250门限列
    wsResult.Range("T1").Value = "测量配置号250门限"
    Call 高效填充公式(wsResult, "T2", "=VLOOKUP(RC[-18],'250'!C[-16]:C[-5],12,0)", lastRowResult)

    ' 转换为数值
    With wsResult.Range("T2:T" & lastRowResult)
        .Copy
        .PasteSpecial Paste:=xlPasteValues
    End With

    ' 清除筛选
    wsUeMeas.AutoFilterMode = False
End Sub

' 优化的整理结果函数
Sub 整理结果()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("异频频点结果")

    ' 批量删除和移动列操作
    ws.Columns("U:U").Delete Shift:=xlToLeft
    ws.Columns("Q:Q").Delete Shift:=xlToLeft

    ' 复制并插入列
    ws.Columns("O:R").Copy
    ws.Columns("E:E").Insert Shift:=xlToRight

    ws.Columns("J:J").Copy
    ws.Columns("D:D").Insert Shift:=xlToRight

    ' 清理多余列
    ws.Columns("K:K").Delete Shift:=xlToLeft
    ws.Columns("S:V").Delete Shift:=xlToLeft

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "整理结果失败: " & Err.Description, vbCritical
End Sub

' 优化的v门限1和门限2并整理TDD函数
Sub v门限1和门限2并整理TDD()
    On Error GoTo ErrorHandler

    Dim appSettings As AppSettings
    Call 保存并优化应用设置(appSettings)

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("异频频点结果")

    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    ' 设置门限1和门限2列
    ws.Range("T1").Value = "门限1"
    ws.Range("U1").Value = "门限2"

    Call 批量设置XLOOKUP公式(ws, "T2", "I2", "UeEUtranMeasurementTDD!M:M", "UeEUtranMeasurementTDD!O:O", lastRow)
    Call 批量设置XLOOKUP公式(ws, "U2", "I2", "UeEUtranMeasurementTDD!M:M", "UeEUtranMeasurementTDD!Q:Q", lastRow)

    ' 设置其他信息列
    ws.Range("V1").Value = "PCI"
    ws.Range("W1").Value = "TAC"
    ws.Range("X1").Value = "频段指示"
    ws.Range("Y1").Value = "中心频点"
    ws.Range("Z1").Value = "带宽"

    Call 批量设置XLOOKUP公式(ws, "V2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!S:S", lastRow)
    Call 批量设置XLOOKUP公式(ws, "W2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!Y:Y", lastRow)
    Call 批量设置XLOOKUP公式(ws, "X2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!AJ:AJ", lastRow)
    Call 批量设置XLOOKUP公式(ws, "Y2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!AK:AK", lastRow)
    Call 批量设置XLOOKUP公式(ws, "Z2", "A2", "EUtranCellTDD!I:I", "EUtranCellTDD!AR:AR", lastRow)

    ' 插入基于覆盖的异频载频测量结果列
    ws.Columns("G:G").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
    ws.Range("G1").Value = "基于覆盖的异频载频测量结果"

    Call 批量设置XLOOKUP公式(ws, "G2", "D2", "CellMeasGroupTDD!I:I", "CellMeasGroupTDD!P:P", lastRow)

    ' 优化的字符串替换操作
    Call 优化字符串替换(ws, "K:K")

Cleanup:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    Exit Sub

ErrorHandler:
    Call 恢复应用设置(appSettings)
    Application.CutCopyMode = False
    MsgBox "v门限1和门限2并整理TDD失败: " & Err.Description, vbCritical
End Sub

' 优化的字符串替换函数
Private Sub 优化字符串替换(ByVal ws As Worksheet, ByVal columnRange As String)
    On Error Resume Next

    ' 使用更安全的替换方法
    Dim specialMark As String
    specialMark = "###MARK###"

    With ws.Range(columnRange)
        ' 步骤1：将最后一个等号替换为特殊标记
        .Replace What:="=*=", Replacement:=specialMark, LookAt:=xlPart, _
            SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
            ReplaceFormat:=False, MatchByte:=False

        ' 步骤2：删除特殊标记前面的所有内容
        .Replace What:="*" & specialMark, Replacement:="", LookAt:=xlPart, _
            SearchOrder:=xlByRows, MatchCase:=False, SearchFormat:=False, _
            ReplaceFormat:=False, MatchByte:=False
    End With

    On Error GoTo 0
End Sub

' ========================================
' 性能监控和调试函数
' ========================================

' 性能测试函数
Sub 性能测试()
    Dim startTime As Double
    Dim endTime As Double

    startTime = Timer

    ' 运行优化版本
    Call 调用全部

    endTime = Timer

    MsgBox "优化版本执行时间: " & Format(endTime - startTime, "0.00") & " 秒", vbInformation, "性能测试结果"
End Sub

' 内存使用情况检查
Sub 检查内存使用()
    Dim memInfo As String
    memInfo = "当前Excel进程内存使用情况:" & vbCrLf
    memInfo = memInfo & "工作簿数量: " & Application.Workbooks.Count & vbCrLf
    memInfo = memInfo & "工作表数量: " & ThisWorkbook.Worksheets.Count & vbCrLf
    memInfo = memInfo & "计算模式: " & IIf(Application.Calculation = xlCalculationAutomatic, "自动", "手动") & vbCrLf
    memInfo = memInfo & "屏幕更新: " & IIf(Application.ScreenUpdating, "开启", "关闭")

    MsgBox memInfo, vbInformation, "内存使用情况"
End Sub

' 简化测试版本 - 用于调试
Sub 测试调用全部()
    On Error GoTo ErrorHandler

    ' 首先检查所有必需的工作表是否存在
    Dim requiredSheets As Variant
    requiredSheets = Array("UeEUtranMeasurementTDD", "UeEUtranMeasurement", "CellMeasGroup", _
                          "CellMeasGroupTDD", "EUtranCellTDD", "EUtranCellMeasurementTDD", _
                          "EUtranCellFDD", "EUtranCellMeasurement", "ECellEquipmentFunction", _
                          "ECellEquipmentFunctionTDD")

    Dim i As Long, missingSheets As String
    For i = 0 To UBound(requiredSheets)
        If Not 验证工作表存在(CStr(requiredSheets(i))) Then
            missingSheets = missingSheets & CStr(requiredSheets(i)) & vbCrLf
        End If
    Next i

    If missingSheets <> "" Then
        MsgBox "以下工作表不存在，请检查:" & vbCrLf & missingSheets, vbCritical, "工作表检查"
        Exit Sub
    End If

    MsgBox "所有必需的工作表都存在，可以开始处理", vbInformation

    ' 如果工作表都存在，则调用原始处理流程
    Call 调用全部

    Exit Sub

ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description & vbCrLf & "错误编号: " & Err.Number, vbCritical
End Sub

' 兼容原始代码的简化版本
Sub 简化列转行()
    On Error GoTo ErrorHandler

    ' 检查源工作表是否存在
    If Not 验证工作表存在("异频频点") Then
        MsgBox "工作表'异频频点'不存在！", vbCritical
        Exit Sub
    End If

    ' 使用原始逻辑但添加错误处理
    Dim wsSource As Worksheet, wsTarget As Worksheet
    Set wsSource = ThisWorkbook.Worksheets("异频频点")

    ' 创建或获取目标工作表
    On Error Resume Next
    Set wsTarget = ThisWorkbook.Worksheets("异频频点结果")
    On Error GoTo ErrorHandler

    If wsTarget Is Nothing Then
        Set wsTarget = ThisWorkbook.Worksheets.Add
        wsTarget.Name = "异频频点结果"
    Else
        wsTarget.Cells.Clear
    End If

    ' 使用原始的逐行处理逻辑（更安全）
    Dim i As Long, j As Long, k As Long
    i = 2
    k = 2

    Do While wsSource.Cells(i, 1) <> ""
        For j = 2 To 33
            If wsSource.Cells(i, j) <> "" Then
                wsTarget.Cells(k, 1) = wsSource.Cells(i, 1)
                wsTarget.Cells(k, 2) = wsSource.Cells(i, j)
                k = k + 1
            End If
        Next j
        i = i + 1
    Loop

    Exit Sub

ErrorHandler:
    MsgBox "简化列转行失败: " & Err.Description, vbCritical
End Sub